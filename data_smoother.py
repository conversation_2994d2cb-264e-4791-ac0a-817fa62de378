import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def moving_average(data, window=5):
    """移动平均平滑"""
    return np.convolve(data, np.ones(window)/window, mode='same')

def exponential_smoothing(data, alpha=0.3):
    """指数平滑"""
    result = np.zeros_like(data)
    result[0] = data[0]
    for i in range(1, len(data)):
        result[i] = alpha * data[i] + (1 - alpha) * result[i-1]
    return result

def median_filter(data, window=5):
    """中值滤波"""
    result = np.copy(data)
    half_window = window // 2
    for i in range(half_window, len(data) - half_window):
        result[i] = np.median(data[i-half_window:i+half_window+1])
    return result

def gaussian_smooth(data, sigma=1.0):
    """简单高斯平滑"""
    window_size = int(6 * sigma) + 1
    if window_size % 2 == 0:
        window_size += 1
    
    # 创建高斯核
    x = np.arange(window_size) - window_size // 2
    kernel = np.exp(-0.5 * (x / sigma) ** 2)
    kernel = kernel / np.sum(kernel)
    
    # 应用卷积
    return np.convolve(data, kernel, mode='same')

def main():
    try:
        # 读取Excel文件
        print("正在读取数据文件...")
        df = pd.read_excel('data.xlsx')
        print(f"数据加载成功，形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 获取数据
        x_data = df.iloc[:, 0].values  # 第一列作为x
        y_data = df.iloc[:, 1].values  # 第二列作为y
        
        # 移除NaN值并排序
        mask = ~(np.isnan(x_data) | np.isnan(y_data))
        x_data = x_data[mask]
        y_data = y_data[mask]
        
        # 按x值排序
        sort_idx = np.argsort(x_data)
        x_data = x_data[sort_idx]
        y_data = y_data[sort_idx]
        
        print(f"有效数据点: {len(x_data)}")
        
        # 应用不同的平滑方法
        print("应用平滑方法...")
        
        # 1. 移动平均 (轻度平滑)
        y_ma5 = moving_average(y_data, window=5)
        
        # 2. 移动平均 (中度平滑)
        y_ma9 = moving_average(y_data, window=9)
        
        # 3. 指数平滑
        y_exp = exponential_smoothing(y_data, alpha=0.3)
        
        # 4. 中值滤波 (去除异常值)
        y_median = median_filter(y_data, window=5)
        
        # 5. 高斯平滑
        y_gauss = gaussian_smooth(y_data, sigma=1.5)
        
        # 创建结果图表
        print("生成对比图...")
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('数据平滑对比', fontsize=16)
        
        # 原始数据
        axes[0, 0].plot(x_data, y_data, 'b-', linewidth=1, alpha=0.7)
        axes[0, 0].set_title('原始数据')
        axes[0, 0].set_xlabel('Electric Field (kV/cm)')
        axes[0, 0].set_ylabel('Polarization (μC/cm²)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 移动平均5
        axes[0, 1].plot(x_data, y_data, 'b-', linewidth=1, alpha=0.3, label='原始')
        axes[0, 1].plot(x_data, y_ma5, 'r-', linewidth=2, label='平滑')
        axes[0, 1].set_title('移动平均 (窗口=5)')
        axes[0, 1].set_xlabel('Electric Field (kV/cm)')
        axes[0, 1].set_ylabel('Polarization (μC/cm²)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].legend()
        
        # 移动平均9
        axes[0, 2].plot(x_data, y_data, 'b-', linewidth=1, alpha=0.3, label='原始')
        axes[0, 2].plot(x_data, y_ma9, 'r-', linewidth=2, label='平滑')
        axes[0, 2].set_title('移动平均 (窗口=9)')
        axes[0, 2].set_xlabel('Electric Field (kV/cm)')
        axes[0, 2].set_ylabel('Polarization (μC/cm²)')
        axes[0, 2].grid(True, alpha=0.3)
        axes[0, 2].legend()
        
        # 指数平滑
        axes[1, 0].plot(x_data, y_data, 'b-', linewidth=1, alpha=0.3, label='原始')
        axes[1, 0].plot(x_data, y_exp, 'r-', linewidth=2, label='平滑')
        axes[1, 0].set_title('指数平滑')
        axes[1, 0].set_xlabel('Electric Field (kV/cm)')
        axes[1, 0].set_ylabel('Polarization (μC/cm²)')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].legend()
        
        # 中值滤波
        axes[1, 1].plot(x_data, y_data, 'b-', linewidth=1, alpha=0.3, label='原始')
        axes[1, 1].plot(x_data, y_median, 'r-', linewidth=2, label='平滑')
        axes[1, 1].set_title('中值滤波')
        axes[1, 1].set_xlabel('Electric Field (kV/cm)')
        axes[1, 1].set_ylabel('Polarization (μC/cm²)')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].legend()
        
        # 高斯平滑
        axes[1, 2].plot(x_data, y_data, 'b-', linewidth=1, alpha=0.3, label='原始')
        axes[1, 2].plot(x_data, y_gauss, 'r-', linewidth=2, label='平滑')
        axes[1, 2].set_title('高斯平滑')
        axes[1, 2].set_xlabel('Electric Field (kV/cm)')
        axes[1, 2].set_ylabel('Polarization (μC/cm²)')
        axes[1, 2].grid(True, alpha=0.3)
        axes[1, 2].legend()
        
        plt.tight_layout()
        plt.savefig('smoothing_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 保存平滑后的数据
        print("保存平滑后的数据...")
        result_df = pd.DataFrame({
            'x_original': x_data,
            'y_original': y_data,
            'y_moving_avg_5': y_ma5,
            'y_moving_avg_9': y_ma9,
            'y_exponential': y_exp,
            'y_median_filter': y_median,
            'y_gaussian': y_gauss
        })
        
        result_df.to_excel('smoothed_data_results.xlsx', index=False)
        print("结果已保存到: smoothed_data_results.xlsx")
        
        # 推荐
        print("\n平滑方法推荐:")
        print("1. 移动平均(窗口=5): 轻度平滑，保持数据特征")
        print("2. 移动平均(窗口=9): 中度平滑，去除更多噪声")
        print("3. 高斯平滑: 平滑效果好，保持曲线形状")
        print("4. 中值滤波: 去除异常值和尖峰毛刺")
        print("5. 指数平滑: 适合有趋势的数据")
        
        print("\n建议:")
        print("- 如果要保持原始数据特征，推荐使用移动平均(窗口=5)")
        print("- 如果毛刺较多，推荐使用高斯平滑或中值滤波")
        print("- 可以根据图表选择最适合的方法")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
