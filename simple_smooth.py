import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def moving_average_smooth(data, window_size=5):
    """移动平均平滑"""
    result = np.copy(data).astype(float)
    half_window = window_size // 2
    
    for i in range(len(data)):
        start = max(0, i - half_window)
        end = min(len(data), i + half_window + 1)
        result[i] = np.mean(data[start:end])
    
    return result

def exponential_smooth(data, alpha=0.3):
    """指数平滑"""
    result = np.zeros_like(data)
    result[0] = data[0]
    
    for i in range(1, len(data)):
        result[i] = alpha * data[i] + (1 - alpha) * result[i-1]
    
    return result

def simple_median_filter(data, kernel_size=5):
    """简单中值滤波"""
    result = np.copy(data).astype(float)
    half_window = kernel_size // 2
    
    for i in range(len(data)):
        start = max(0, i - half_window)
        end = min(len(data), i + half_window + 1)
        result[i] = np.median(data[start:end])
    
    return result

def gaussian_like_smooth(data, sigma=1.0):
    """类高斯平滑（使用加权移动平均）"""
    window_size = int(6 * sigma) + 1
    if window_size % 2 == 0:
        window_size += 1
    
    half_window = window_size // 2
    
    # 创建高斯权重
    x = np.arange(-half_window, half_window + 1)
    weights = np.exp(-0.5 * (x / sigma) ** 2)
    weights = weights / np.sum(weights)
    
    result = np.copy(data).astype(float)
    
    for i in range(len(data)):
        start = max(0, i - half_window)
        end = min(len(data), i + half_window + 1)
        
        # 调整权重以匹配实际窗口大小
        w_start = max(0, half_window - i)
        w_end = w_start + (end - start)
        
        if w_end <= len(weights):
            window_weights = weights[w_start:w_end]
            window_weights = window_weights / np.sum(window_weights)
            result[i] = np.sum(data[start:end] * window_weights)
        else:
            result[i] = np.mean(data[start:end])
    
    return result

def load_and_process_data():
    """加载并处理数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel('data.xlsx')
        print(f"数据加载成功，形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        
        # 获取数值列
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if len(numeric_cols) < 2:
            print("错误: 需要至少两列数值数据")
            return None, None, None
        
        x_col = numeric_cols[0]
        y_col = numeric_cols[1]
        
        print(f"使用列: X轴={x_col}, Y轴={y_col}")
        
        x_data = df[x_col].values
        y_data = df[y_col].values
        
        # 移除NaN值
        mask = ~(np.isnan(x_data) | np.isnan(y_data))
        x_data = x_data[mask]
        y_data = y_data[mask]
        
        # 按x值排序
        sort_indices = np.argsort(x_data)
        x_data = x_data[sort_indices]
        y_data = y_data[sort_indices]
        
        print(f"有效数据点数: {len(x_data)}")
        
        return x_data, y_data, df.columns.tolist()
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None, None, None

def apply_smoothing_methods(y_data):
    """应用各种平滑方法"""
    methods = {}
    
    # 1. 移动平均 (窗口大小5)
    methods['移动平均_5'] = moving_average_smooth(y_data, window_size=5)
    
    # 2. 移动平均 (窗口大小9)
    methods['移动平均_9'] = moving_average_smooth(y_data, window_size=9)
    
    # 3. 指数平滑
    methods['指数平滑'] = exponential_smooth(y_data, alpha=0.3)
    
    # 4. 中值滤波
    methods['中值滤波'] = simple_median_filter(y_data, kernel_size=5)
    
    # 5. 类高斯平滑
    methods['类高斯平滑'] = gaussian_like_smooth(y_data, sigma=1.5)
    
    return methods

def plot_results(x_data, y_data, smoothed_methods):
    """绘制结果"""
    plt.figure(figsize=(15, 12))
    
    # 原始数据
    plt.subplot(2, 3, 1)
    plt.plot(x_data, y_data, 'b-', linewidth=1, alpha=0.7)
    plt.title('原始数据')
    plt.xlabel('Electric Field (kV/cm)')
    plt.ylabel('Polarization (μC/cm²)')
    plt.grid(True, alpha=0.3)
    
    # 各种平滑方法
    methods = list(smoothed_methods.keys())
    for i, method in enumerate(methods):
        plt.subplot(2, 3, i+2)
        plt.plot(x_data, y_data, 'b-', linewidth=1, alpha=0.3, label='原始')
        plt.plot(x_data, smoothed_methods[method], 'r-', linewidth=2, label=method)
        plt.title(method)
        plt.xlabel('Electric Field (kV/cm)')
        plt.ylabel('Polarization (μC/cm²)')
        plt.grid(True, alpha=0.3)
        plt.legend()
    
    plt.tight_layout()
    plt.savefig('smoothing_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("图表已保存为 'smoothing_results.png'")

def save_results(x_data, y_data, smoothed_methods, original_columns):
    """保存结果到Excel文件"""
    # 创建结果DataFrame
    result_df = pd.DataFrame({
        original_columns[0]: x_data,
        original_columns[1]: y_data
    })
    
    # 添加所有平滑结果
    for method_name, smoothed_data in smoothed_methods.items():
        result_df[f'{original_columns[1]}_平滑_{method_name}'] = smoothed_data
    
    # 保存到Excel
    output_filename = 'smoothed_results.xlsx'
    result_df.to_excel(output_filename, index=False)
    print(f"所有平滑结果已保存到: {output_filename}")
    
    return output_filename

def main():
    print("开始数据平滑处理...")
    
    # 加载数据
    x_data, y_data, original_columns = load_and_process_data()
    if x_data is None:
        return
    
    # 应用平滑方法
    print("\n应用平滑方法...")
    smoothed_methods = apply_smoothing_methods(y_data)
    
    # 绘制结果
    print("\n生成对比图...")
    plot_results(x_data, y_data, smoothed_methods)
    
    # 保存结果
    print("\n保存结果...")
    save_results(x_data, y_data, smoothed_methods, original_columns)
    
    print("\n推荐:")
    print("- 移动平均_5: 轻度平滑，保持数据特征")
    print("- 移动平均_9: 中度平滑，去除更多噪声")
    print("- 类高斯平滑: 平滑效果好，保持曲线形状")
    print("- 中值滤波: 去除异常值和尖峰")
    print("- 指数平滑: 适合趋势数据")

if __name__ == "__main__":
    main()
