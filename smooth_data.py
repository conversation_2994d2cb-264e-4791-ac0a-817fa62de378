import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
try:
    from scipy import signal
    from scipy.ndimage import gaussian_filter1d
    from scipy.interpolate import UnivariateSpline
    SCIPY_AVAILABLE = True
except ImportError:
    print("SciPy未安装，将使用基础平滑方法")
    SCIPY_AVAILABLE = False
import warnings
warnings.filterwarnings('ignore')

def load_data(filename):
    """加载Excel数据"""
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(filename)
        print(f"数据文件加载成功，形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def moving_average_smooth(data, window_size=5):
    """移动平均平滑"""
    return pd.Series(data).rolling(window=window_size, center=True).mean().fillna(method='bfill').fillna(method='ffill')

def savgol_smooth(data, window_length=11, polyorder=3):
    """Sa<PERSON><PERSON><PERSON>-Go<PERSON>滤波平滑"""
    if not SCIPY_AVAILABLE:
        return moving_average_smooth(data, window_length//2)
    if len(data) < window_length:
        window_length = len(data) if len(data) % 2 == 1 else len(data) - 1
    if window_length < polyorder + 1:
        polyorder = window_length - 2 if window_length > 2 else 1
    return signal.savgol_filter(data, window_length, polyorder)

def gaussian_smooth(data, sigma=1.0):
    """高斯滤波平滑"""
    if not SCIPY_AVAILABLE:
        return moving_average_smooth(data, int(sigma*3))
    return gaussian_filter1d(data, sigma=sigma)

def spline_smooth(x, y, smoothing_factor=None):
    """样条插值平滑"""
    if not SCIPY_AVAILABLE:
        return moving_average_smooth(y, 5)
    if smoothing_factor is None:
        smoothing_factor = len(x) * 0.1
    spline = UnivariateSpline(x, y, s=smoothing_factor)
    return spline(x)

def median_filter_smooth(data, kernel_size=5):
    """中值滤波平滑"""
    if not SCIPY_AVAILABLE:
        # 简单的中值滤波实现
        result = np.copy(data)
        half_window = kernel_size // 2
        for i in range(half_window, len(data) - half_window):
            result[i] = np.median(data[i-half_window:i+half_window+1])
        return result
    return signal.medfilt(data, kernel_size=kernel_size)

def plot_comparison(x, original, smoothed_methods, title="数据平滑对比"):
    """绘制原始数据和平滑后数据的对比图"""
    plt.figure(figsize=(15, 10))
    
    # 原始数据
    plt.subplot(2, 3, 1)
    plt.plot(x, original, 'b-', linewidth=1, alpha=0.7, label='原始数据')
    plt.title('原始数据')
    plt.xlabel('Electric Field (kV/cm)')
    plt.ylabel('Polarization (μC/cm²)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 各种平滑方法
    methods = list(smoothed_methods.keys())
    for i, method in enumerate(methods):
        plt.subplot(2, 3, i+2)
        plt.plot(x, original, 'b-', linewidth=1, alpha=0.3, label='原始数据')
        plt.plot(x, smoothed_methods[method], 'r-', linewidth=2, label=f'{method}平滑')
        plt.title(f'{method}平滑')
        plt.xlabel('Electric Field (kV/cm)')
        plt.ylabel('Polarization (μC/cm²)')
        plt.grid(True, alpha=0.3)
        plt.legend()
    
    plt.tight_layout()
    plt.savefig('smoothing_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def save_smoothed_data(df, x_col, y_col, smoothed_data, method_name, filename):
    """保存平滑后的数据到新的Excel文件"""
    new_df = df.copy()
    new_df[f'{y_col}_smoothed_{method_name}'] = smoothed_data
    
    output_filename = f'smoothed_data_{method_name}.xlsx'
    new_df.to_excel(output_filename, index=False)
    print(f"平滑后的数据已保存到: {output_filename}")
    return output_filename

def main():
    # 加载数据
    df = load_data('data.xlsx')
    if df is None:
        return
    
    print("\n数据预览:")
    print(df.head())
    
    # 自动检测可能的x和y列
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    print(f"\n数值列: {numeric_cols}")
    
    if len(numeric_cols) < 2:
        print("错误: 需要至少两列数值数据")
        return
    
    # 假设第一列是x轴，第二列是y轴（可以根据实际情况调整）
    x_col = numeric_cols[0]
    y_col = numeric_cols[1]
    
    print(f"\n使用列: X轴={x_col}, Y轴={y_col}")
    
    x_data = df[x_col].values
    y_data = df[y_col].values
    
    # 移除NaN值
    mask = ~(np.isnan(x_data) | np.isnan(y_data))
    x_data = x_data[mask]
    y_data = y_data[mask]

    # 按x值排序（样条插值需要）
    sort_indices = np.argsort(x_data)
    x_data = x_data[sort_indices]
    y_data = y_data[sort_indices]

    print(f"有效数据点数: {len(x_data)}")

    # 应用不同的平滑方法
    smoothed_methods = {}

    # 1. 移动平均
    smoothed_methods['移动平均'] = moving_average_smooth(y_data, window_size=5)

    # 2. Savitzky-Golay滤波
    smoothed_methods['SavGol滤波'] = savgol_smooth(y_data, window_length=11, polyorder=3)

    # 3. 高斯滤波
    smoothed_methods['高斯滤波'] = gaussian_smooth(y_data, sigma=1.5)

    # 4. 样条插值
    smoothed_methods['样条插值'] = spline_smooth(x_data, y_data, smoothing_factor=len(x_data)*0.1)

    # 5. 中值滤波
    smoothed_methods['中值滤波'] = median_filter_smooth(y_data, kernel_size=5)
    
    # 绘制对比图
    plot_comparison(x_data, y_data, smoothed_methods)
    
    # 保存所有平滑结果
    print("\n保存平滑后的数据...")
    for method_name, smoothed_data in smoothed_methods.items():
        # 创建完整的DataFrame（包含原始数据）
        result_df = pd.DataFrame({
            x_col: x_data,
            y_col: y_data,
            f'{y_col}_smoothed': smoothed_data
        })
        
        output_filename = f'smoothed_data_{method_name}.xlsx'
        result_df.to_excel(output_filename, index=False)
        print(f"  - {method_name}: {output_filename}")
    
    # 推荐最佳方法
    print("\n推荐:")
    print("- 对于去除高频噪声: 使用'高斯滤波'或'SavGol滤波'")
    print("- 对于保持数据特征: 使用'样条插值'")
    print("- 对于去除异常值: 使用'中值滤波'")
    print("- 对于简单平滑: 使用'移动平均'")

if __name__ == "__main__":
    main()
